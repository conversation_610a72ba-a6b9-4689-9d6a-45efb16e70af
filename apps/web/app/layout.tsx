import './globals.css'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Fishivo - Balıkçıların Sosyal Ağı',
  description: 'Avlarını paylaş, balıkçı topluluğuna katıl, yeni teknikler öğren. En büyük balıkçılık sosyal platformu.',
  keywords: 'balıkçılık, fishing, sosyal ağ, balık avı, balık paylaşım, balıkçı topluluğu',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="tr" className="scroll-smooth">
      <body className="antialiased bg-gradient-to-br from-blue-50 via-white to-green-50">
        {children}
      </body>
    </html>
  )
} 