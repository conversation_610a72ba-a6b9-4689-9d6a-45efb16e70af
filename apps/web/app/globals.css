@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
  --primary: #007AFF;
  --secondary: #5856D6;
  --success: #34C759;
  --warning: #FF9500;
  --error: #FF3B30;
  --water: #4A90E2;
  --fish: #FF6B35;
  --nature: #7ED321;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, sans-serif;
}

body {
  @apply bg-white dark:bg-dark-primary text-gray-900 dark:text-white;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-gradient {
  @apply bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-dark-primary dark:via-dark-secondary dark:to-dark-primary;
}

.card-hover {
  @apply transition-all duration-300 ease-in-out;
}

.card-hover:hover {
  @apply transform -translate-y-2 shadow-card-hover dark:shadow-dark-card-hover;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

.animate-wave {
  animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(5deg); }
  75% { transform: rotate(-5deg); }
}

.glass-effect {
  @apply backdrop-blur-sm bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/20;
}

.neon-glow {
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.3);
}

.dark .neon-glow {
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.5);
} 