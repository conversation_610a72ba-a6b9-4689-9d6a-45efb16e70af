'use client'

import { useState } from 'react'
import Link from 'next/link'
import { theme } from '@fishivo/shared'
import ThemeToggle from './ThemeToggle'

export default function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const menuItems = [
    { href: '#features', label: '<PERSON>zellikler' },
    { href: '#community', label: 'Topluluk' },
    { href: '#download', label: 'İndir' },
    { href: '/dashboard', label: '<PERSON><PERSON><PERSON>' },
  ]

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 glass-effect border-b border-gray-200/20 dark:border-gray-700/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-primary to-water rounded-xl 
                              flex items-center justify-center shadow-lg group-hover:shadow-xl 
                              transition-all duration-300 group-hover:scale-110">
                <svg className="w-6 h-6 text-white animate-wave" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.59 10.75C10.21 11.13 10.21 11.75 10.59 12.13L11.79 13.33L7 18.12C6.6 18.52 6.6 19.16 7 19.56S8.04 19.96 8.44 19.56L13.23 14.77L14.43 15.97C14.81 16.35 15.43 16.35 15.81 15.97L21.41 10.37L22.92 11.88L24 10.5L21 9Z"/>
                </svg>
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-fish rounded-full animate-ping opacity-75"></div>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold gradient-text">Fishivo</h1>
              <p className="text-xs text-gray-600 dark:text-gray-400">Balıkçıların Sosyal Ağı</p>
            </div>
          </Link>

          {/* Desktop Menu */}
          <div className="hidden lg:flex items-center space-x-8">
            {menuItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary 
                          transition-colors duration-200 font-medium relative group"
              >
                {item.label}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all 
                                duration-300 group-hover:w-full rounded-full"></span>
              </Link>
            ))}
            <ThemeToggle />
            <Link
              href="/dashboard"
              className="bg-gradient-to-r from-primary to-secondary text-white px-6 py-2 
                        rounded-full font-semibold hover:shadow-lg hover:scale-105 
                        transition-all duration-300 neon-glow"
            >
              Uygulamayı Aç
            </Link>
          </div>

          {/* Mobile Menu Button & Theme Toggle */}
          <div className="flex items-center space-x-4 lg:hidden">
            <ThemeToggle />
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 dark:text-gray-300 hover:text-primary p-2 rounded-lg
                        hover:bg-gray-100 dark:hover:bg-dark-card transition-colors duration-200"
              aria-label="Toggle menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden py-4 border-t border-gray-200/20 dark:border-gray-700/20">
            <div className="flex flex-col space-y-4">
              {menuItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={() => setIsMenuOpen(false)}
                  className="text-gray-700 dark:text-gray-300 hover:text-primary 
                            transition-colors duration-200 font-medium px-4 py-2 rounded-lg
                            hover:bg-gray-100 dark:hover:bg-dark-card"
                >
                  {item.label}
                </Link>
              ))}
              <div className="px-4 pt-2">
                <Link
                  href="/dashboard"
                  onClick={() => setIsMenuOpen(false)}
                  className="bg-gradient-to-r from-primary to-secondary text-white px-6 py-3 
                            rounded-full font-semibold text-center block hover:shadow-lg 
                            transition-all duration-300"
                >
                  Uygulamayı Aç
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
} 