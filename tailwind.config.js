/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './apps/web/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: '#007AFF',
        secondary: '#5856D6',
        success: '#34C759',
        warning: '#FF9500',
        error: '#FF3B30',
        water: '#4A90E2',
        fish: '#FF6B35',
        nature: '#7ED321',
        dark: {
          100: '#1C1C1E',
          200: '#2C2C2E',
          300: '#3A3A3C',
          400: '#48484A',
          500: '#636366',
          600: '#8E8E93',
          700: '#AEAEB2',
          800: '#C7C7CC',
          900: '#F2F2F7',
        }
      },
      backgroundColor: {
        'dark-primary': '#121212',
        'dark-secondary': '#1E1E1E',
        'dark-card': '#2A2A2A',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      boxShadow: {
        'card': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'card-hover': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'dark-card': '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',
        'dark-card-hover': '0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
} 