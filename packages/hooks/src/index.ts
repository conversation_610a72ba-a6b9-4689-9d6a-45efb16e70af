// Authentication hooks
export { useAuth } from './hooks/useAuth';
export { useProfile } from './hooks/useProfile';

// Social hooks
export { useFollow } from './hooks/useFollow';
export { useLikes } from './hooks/useLikes';

// Data hooks - using real API methods
export { useCatches } from './hooks/useCatches';
export { useUsers } from './hooks/useUsers';

// Location and Weather hooks
export { useLocation } from './hooks/useLocation';
export { useWeather } from './hooks/useWeather';

// Utility hooks
export { useImagePicker } from './hooks/useImagePicker';

// Form and Validation hooks
export { useForm } from './hooks/useForm';
export { useValidation } from './hooks/useValidation';

// Storage and Navigation hooks
export { useStorage } from './hooks/useStorage';

// Navigation hooks
export { useNavigation } from './hooks/useNavigation';
export { useRoute } from './hooks/useRoute';

// Contexts
export { AuthProvider } from './contexts/AuthContext';
export { FollowProvider } from './contexts/FollowContext';
export { useAuth as useAuthContext } from './contexts/AuthContext';
export { useFollow as useFollowContext } from './contexts/FollowContext';

// Context exports (excluding useAuth to avoid conflict)
export { AuthProvider, AuthContext } from './contexts/AuthContext';
export * from './contexts/FollowContext';
export * from './contexts/UnitsContext';
export * from './contexts/LocationContext';

// Core API hooks
export { useApi } from './useApi';
export { useSupabase } from './useSupabase';